/* ملف CSS لتجاوز تنسيقات DataTables وضمان اللون الأزرق المتدرج */

/* تنسيقات قوية جداً لتجاوز DataTables CSS - أولوية عالية جداً */
table.dataTable thead th,
table.dataTable.display thead th,
.dataTables_wrapper table.dataTable thead th,
.dataTables_wrapper table thead th,
.dataTables_wrapper th,
.dataTables_wrapper thead th,
#rewardsDataTable.dataTable thead th,
#deductionsDataTable.dataTable thead th,
#rewardsDataTable thead th,
#deductionsDataTable thead th,
table.dataTable th,
.dataTable th,
.display th,
table.dataTable thead th.sorting,
table.dataTable thead th.sorting_asc,
table.dataTable thead th.sorting_desc,
.dataTables_wrapper table.dataTable thead th.sorting,
.dataTables_wrapper table.dataTable thead th.sorting_asc,
.dataTables_wrapper table.dataTable thead th.sorting_desc {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  background-image: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  border-bottom: 2px solid #0D5EA6 !important;
}

/* تنسيقات خاصة لجداول المكافآت والخصومات */
.rewards-deductions-page table.dataTable thead th,
.rewards-deductions-page .dataTables_wrapper table thead th,
.rewards-deductions-page .dataTables_wrapper th,
.rewards-deductions-page #rewardsDataTable thead th,
.rewards-deductions-page #deductionsDataTable thead th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* إزالة أي تنسيقات متضاربة من DataTables */
.dataTables_wrapper table.dataTable thead th.sorting,
.dataTables_wrapper table.dataTable thead th.sorting_asc,
.dataTables_wrapper table.dataTable thead th.sorting_desc,
.dataTables_wrapper table.dataTable thead th.sorting_asc_disabled,
.dataTables_wrapper table.dataTable thead th.sorting_desc_disabled {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
}

/* تنسيقات hover للرؤوس */
.dataTables_wrapper table.dataTable thead th:hover,
table.dataTable thead th:hover {
  background: linear-gradient(135deg, #0D5EA6 0%, #1976D2 50%, #0D5EA6 100%) !important;
  background-color: #0D5EA6 !important;
  color: white !important;
}

/* تنسيقات خاصة للأيقونات في الرؤوس */
.dataTables_wrapper table.dataTable thead th.sorting:after,
.dataTables_wrapper table.dataTable thead th.sorting_asc:after,
.dataTables_wrapper table.dataTable thead th.sorting_desc:after {
  color: white !important;
  opacity: 0.7 !important;
}

/* تأكيد التنسيقات لجميع حالات DataTables */
.dataTables_wrapper .dataTable thead th,
.dataTables_wrapper .dataTable th,
.jquery-datatable thead th,
.jquery-datatable th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* تنسيقات إضافية للتأكد من التطبيق */
body .dataTables_wrapper table thead th,
body table.dataTable thead th,
body .dataTable thead th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* تنسيقات خاصة للجداول المحددة بالـ ID */
#rewardsDataTable.display thead th,
#deductionsDataTable.display thead th,
#rewardsDataTable.dataTable thead th,
#deductionsDataTable.dataTable thead th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  border-bottom: 2px solid #0D5EA6 !important;
}

/* تنسيقات لضمان عدم تأثر الجداول بأي CSS خارجي */
.main-content table.dataTable thead th,
.main-content .dataTables_wrapper table thead th,
.main-content .dataTables_wrapper th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* إزالة أي خلفيات أخرى قد تكون مطبقة */
.dataTables_wrapper table thead th[style],
table.dataTable thead th[style],
.dataTable thead th[style] {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
}

/* تنسيقات خاصة لحالات الفرز */
.dataTables_wrapper table.dataTable thead .sorting,
.dataTables_wrapper table.dataTable thead .sorting_asc,
.dataTables_wrapper table.dataTable thead .sorting_desc {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
  cursor: pointer !important;
}

/* تنسيقات للتأكد من عدم وجود ألوان أخرى */
.dataTables_wrapper table.dataTable thead th:not(.green-allowed),
table.dataTable thead th:not(.green-allowed),
.dataTable thead th:not(.green-allowed) {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  color: white !important;
  border-color: #0D5EA6 !important;
}
/* 
تنسيقات إضافية بأولوية عالية جداً */
html body .rewards-deductions-page table.dataTable thead th,
html body .rewards-deductions-page .dataTables_wrapper table thead th,
html body .rewards-deductions-page #rewardsDataTable thead th,
html body .rewards-deductions-page #deductionsDataTable thead th,
html body .main-content table.dataTable thead th,
html body .main-content .dataTables_wrapper table thead th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  background-image: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* تنسيقات لجميع حالات الفرز في DataTables */
.dataTables_wrapper table.dataTable thead .sorting:before,
.dataTables_wrapper table.dataTable thead .sorting:after,
.dataTables_wrapper table.dataTable thead .sorting_asc:before,
.dataTables_wrapper table.dataTable thead .sorting_asc:after,
.dataTables_wrapper table.dataTable thead .sorting_desc:before,
.dataTables_wrapper table.dataTable thead .sorting_desc:after {
  color: white !important;
  opacity: 0.7 !important;
}

/* إزالة أي خلفيات أخرى */
.dataTables_wrapper table.dataTable thead th[style*="background"],
table.dataTable thead th[style*="background"] {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  background-image: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
}

/* تنسيقات خاصة للجداول المحددة */
#rewardsDataTable_wrapper table thead th,
#deductionsDataTable_wrapper table thead th,
#rewardsDataTable.display.dataTable thead th,
#deductionsDataTable.display.dataTable thead th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  background-image: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* تنسيقات لضمان عدم تأثر الجداول بأي تنسيقات أخرى */
.rewards-deductions-page .dataTables_wrapper table.dataTable thead th,
.rewards-deductions-page table.dataTable thead th,
.rewards-deductions-page .display thead th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  background-image: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  border-bottom: 2px solid #0D5EA6 !important;
}

/* تنسيقات نهائية بأولوية قصوى */
body .dataTables_wrapper table.dataTable thead th,
body table.dataTable thead th,
body .dataTable thead th,
body .display thead th {
  background: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  background-color: #1976D2 !important;
  background-image: linear-gradient(135deg, #1976D2 0%, #0D5EA6 50%, #1976D2 100%) !important;
  color: white !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}