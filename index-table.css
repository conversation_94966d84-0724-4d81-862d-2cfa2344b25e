/* جدول عصري أزرق ثابت مع فواصل واضحة لقاعدة بيانات الموظفين */

.employee-table-container {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 4px 24px var(--shadow-color);
  padding: 24px 16px;
  margin-top: 24px;
  overflow-x: auto;
}

/* تم نقل تنسيقات الجداول إلى shared-styles.css */

/* تم نقل تنسيقات الأزرار إلى shared-styles.css */

/* تم نقل تنسيقات زر العرض إلى shared-styles.css */

@media (max-width: 700px) {
  .employee-table thead th, 
  .employee-table tbody td {
    font-size: 0.95rem;
    padding: 8px 4px;
  }
  
  .employee-table-container {
    padding: 8px 2px;
  }
}

/* تم نقل تنسيقات .table-controls إلى shared-styles.css */

.export-btn {
  background: var(--primary-color);
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
}

.export-btn:hover {
  background: var(--primary-dark);
}

.delete-all {
  background-color: var(--danger-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.delete-all:hover {
  background-color: var(--danger-dark);
}

.calculate-leave {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.calculate-leave:hover {
  background-color: #218838;
}

.actions-bar {
  display: flex;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  margin: 5% auto;
  padding: 0;
  border-radius: 12px;
  width: 80%;
  max-width: 600px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.close {
  color: var(--text-color);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: var(--danger-color);
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Field Selection Styles */
.field-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.field-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.field-checkbox label {
  cursor: pointer;
  font-size: 1rem;
}

/* Button Styles */
.confirm-btn, .cancel-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.confirm-btn {
  background: var(--primary-color);
  color: #ffffff;
}

.confirm-btn:hover {
  background: var(--primary-dark);
}

.cancel-btn {
  background: #f0f0f0;
  color: #333;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

@media (max-width: 700px) {
  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
  
  .fields-grid {
    grid-template-columns: 1fr;
  }
}
